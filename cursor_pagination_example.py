#!/usr/bin/env python3
"""
Example demonstrating cursor-based pagination with the custom dataset functions.
"""

from simple_limited_dataset import fetch_dataset_page, init_limited_dataset

def example_cursor_pagination():
    """
    Example showing how to use cursor-based pagination to fetch records in pages.
    """
    print("=== Cursor-based Pagination Example ===")
    
    cursor = None
    page_num = 1
    total_records = 0
    
    try:
        while True:
            print(f"\nFetching page {page_num}...")
            
            # Fetch a page of records
            records, next_cursor, has_more = fetch_dataset_page(
                project="pedro-project1", 
                name="themes", 
                limit=2,  # 2 records per page
                cursor=cursor
            )
            
            print(f"Page {page_num}: Got {len(records)} records")
            print(f"Next cursor: {next_cursor}")
            print(f"Has more records: {has_more}")
            
            total_records += len(records)
            
            # Print the records
            for i, record in enumerate(records):
                print(f"  Record {i}: {record}")
            
            # Stop if no more records or we've fetched enough for demo
            if not has_more or page_num >= 3:  # Stop after 3 pages for demo
                break
                
            cursor = next_cursor
            page_num += 1
        
        print(f"\nSummary:")
        print(f"Total pages fetched: {page_num}")
        print(f"Total records fetched: {total_records}")
        print(f"Final cursor: {cursor}")
        print(f"Has more records: {has_more}")
        
        # You can save the cursor and use it later to continue pagination
        if has_more and cursor:
            print(f"\nTo continue pagination later, use cursor: {cursor}")
            
    except Exception as e:
        print(f"Error during pagination: {e}")


def example_simple_limit():
    """
    Example showing simple record limiting (your original use case).
    """
    print("\n=== Simple Limit Example ===")
    
    try:
        # This will only fetch 1 record (your original requirement)
        dataset = init_limited_dataset(
            project="pedro-project1", 
            name="themes", 
            max_records=1
        )
        
        records = []
        for record in dataset:
            records.append(record)
        
        print(f"Fetched {len(records)} records")
        for i, record in enumerate(records):
            print(f"Record {i}: {record}")
            
    except Exception as e:
        print(f"Error: {e}")


def example_manual_pagination_control():
    """
    Example showing how to manually control pagination with cursors.
    """
    print("\n=== Manual Pagination Control Example ===")
    
    try:
        # Start with first page
        records, cursor, has_more = fetch_dataset_page(
            project="pedro-project1", 
            name="themes", 
            limit=3
        )
        
        print(f"First page: {len(records)} records")
        print(f"Cursor for next page: {cursor}")
        
        # Save cursor for later use
        saved_cursor = cursor
        
        # Later, you can resume from where you left off
        if saved_cursor and has_more:
            print("\nResuming from saved cursor...")
            more_records, next_cursor, still_has_more = fetch_dataset_page(
                project="pedro-project1", 
                name="themes", 
                limit=3,
                cursor=saved_cursor
            )
            
            print(f"Next page: {len(more_records)} records")
            print(f"Next cursor: {next_cursor}")
            print(f"Still has more: {still_has_more}")
            
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    print("Dataset Pagination Examples")
    print("=" * 50)
    
    # Run examples
    example_simple_limit()
    example_cursor_pagination() 
    example_manual_pagination_control()
    
    print("\n" + "=" * 50)
    print("Examples complete!")
    
    print("\nKey Functions Available:")
    print("1. init_limited_dataset() - Simple wrapper with max_records limit")
    print("2. fetch_dataset_page() - Direct cursor-based pagination")
    print("3. Both return cursors for manual pagination control")
